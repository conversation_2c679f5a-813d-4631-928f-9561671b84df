import { ref, computed, type Ref, onUnmounted } from 'vue'
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  updateDoc,
  serverTimestamp,
  addDoc,
  deleteDoc,
  onSnapshot,
  orderBy,
  type DocumentData,
  type QuerySnapshot,
} from 'firebase/firestore'
import { useFirebase } from './useFirebase'
import { User } from '@/models/User.ts'
import type { UserData, UserPreferences } from '@/models/User.ts'
import { useActivityLog } from './useActivityLog'
import type { ActivityType } from './useActivityLog'

export function useUsers() {
  const { db, auth } = useFirebase()
  const { logActivity } = useActivityLog()
  const users = ref<User[]>([]) as Ref<User[]>
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const success = ref<string | null>(null)
  let unsubscribe: (() => void) | null = null

  // Sort users by name
  const sortedUsers = computed(() => {
    return [...users.value].sort((a, b) => a.fullName.localeCompare(b.fullName))
  })

  // Filter active users
  const activeUsers = computed(() => {
    return sortedUsers.value.filter(user => !user.isDisabled)
  })

  // Check if email exists in subscribers collection
  async function checkSubscriberStatus(email: string): Promise<boolean> {
    try {
      const q = query(
        collection(db, 'subscribers'),
        where('email', '==', email),
      )
      const snapshot = await getDocs(q)
      return !snapshot.empty
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
      return false
    }
  }

  // Get a single user by ID
  async function getUser(userId: string): Promise<User | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', userId))
      if (!userDoc.exists()) return null

      const userData = userDoc.data() as UserData
      userData.id = userDoc.id

      // If this is the current user, sync photoURL from Firebase Auth
      if (userId === auth.currentUser?.uid) {
        userData.photoURL = auth.currentUser.photoURL || userData.photoURL
      }

      // Check subscriber status
      const isSubscribed = await checkSubscriberStatus(userData.email)

      // Update user preferences if subscription status is different
      if (isSubscribed !== userData.prefs?.isSubscribed) {
        const userRef = doc(db, 'users', userId)
        await updateDoc(userRef, {
          'prefs.isSubscribed': isSubscribed,
          updatedAt: serverTimestamp(),
        })
        userData.prefs = {
          ...userData.prefs,
          isSubscribed,
          viewAsGrid: userData.prefs?.viewAsGrid ?? false,
        }
      }

      // Get admin claim from Firebase Auth token
      const token = await auth.currentUser?.getIdTokenResult()
      const isAdmin = token?.claims?.admin || false

      // If this is the current user, merge admin status
      if (userId === auth.currentUser?.uid) {
        userData.roles = userData.roles || []
        if (isAdmin) {
          if (!userData.roles.some(role => role.type === 'admin')) {
            userData.roles.push({ type: 'admin' })
          }
        }
      }

      return User.fromFirestore(userData)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
      return null
    }
  }

  // Subscribe to users collection
  function subscribeToUsers(): void {
    isLoading.value = true
    error.value = null

    try {
      const usersRef = collection(db, 'users')
      const q = query(usersRef, orderBy('firstName'), orderBy('lastName'))

      unsubscribe = onSnapshot(
        q,
        (snapshot: QuerySnapshot<DocumentData>) => {
          const changes: string[] = []
          snapshot.docChanges().forEach(change => {
            const data = {
              id: change.doc.id,
              ...change.doc.data(),
            } as UserData
            const user = new User(data)

            switch (change.type) {
              case 'modified':
                changes.push(`Updated: ${user.firstName} ${user.lastName}`)
                break
              case 'removed':
                changes.push(`Removed: ${user.firstName} ${user.lastName}`)
                break
            }
          })

          users.value = snapshot.docs.map(doc => {
            const data = {
              id: doc.id,
              ...doc.data(),
            } as UserData
            return new User(data)
          })

          if (changes.length > 0) {
            success.value = changes.join(', ')
            setTimeout(() => {
              success.value = null
            }, 3000)
          }
        },
        (err: unknown) => {
          console.error('Error subscribing to users:', err)
          error.value = `Failed to subscribe to users: ${err instanceof Error ? err.message : 'Unknown error'}`
        },
      )
    } catch (err: unknown) {
      console.error('Error setting up subscription:', err)
      error.value = `Failed to set up subscription: ${err instanceof Error ? err.message : 'Unknown error'}`
    } finally {
      isLoading.value = false
    }
  }

  // Upsert a user with role data
  async function upsertUser(
    userData: Partial<UserData>,
    roleData: Record<string, any> | null = null,
  ): Promise<boolean> {
    try {
      if (!userData.id) throw new Error('User ID is required')

      const userRef = doc(db, 'users', userData.id)
      const data = {
        ...userData,
        updatedAt: serverTimestamp(),
      }

      if (roleData) {
        data.roles = userData.roles || []
        const existingRoleIndex = data.roles.findIndex(
          r => r.type === roleData.type,
        )

        if (existingRoleIndex >= 0) {
          // Update existing role
          data.roles[existingRoleIndex] = {
            ...data.roles[existingRoleIndex],
            ...roleData.data,
          }
        } else {
          // Add new role
          data.roles.push({ type: roleData.type, ...roleData.data })
          // Log role added
          await logActivity('ROLE_ADDED' as ActivityType, {
            userId: userData.id,
            roleType: roleData.type,
            roleData: roleData.data,
          })
        }
      } else if (userData.roles) {
        // Check for removed roles by comparing with existing roles
        const currentDoc = await getDoc(userRef)
        const currentData = currentDoc.data() as UserData
        const currentRoles = currentData.roles || []

        // Find removed roles
        const removedRoles = currentRoles.filter(
          currentRole =>
            !userData.roles?.some(newRole => newRole.type === currentRole.type),
        )

        // Log each removed role
        for (const removedRole of removedRoles) {
          await logActivity('ROLE_REMOVED' as ActivityType, {
            userId: userData.id,
            roleType: removedRole.type,
          })
        }
      }

      await updateDoc(userRef, data)
      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
      return false
    }
  }

  // Update user preferences
  async function updateUserPreferences(
    userId: string,
    prefs: UserPreferences,
  ): Promise<boolean> {
    try {
      const userRef = doc(db, 'users', userId)

      if ('isSubscribed' in prefs) {
        const userData = (await getDoc(userRef)).data() as UserData
        if (prefs.isSubscribed) {
          const subscribersRef = collection(db, 'subscribers')
          const q = query(subscribersRef, where('email', '==', userData.email))
          const snapshot = await getDocs(q)

          if (snapshot.empty) {
            await addDoc(subscribersRef, {
              email: userData.email,
              name: `${userData.firstName} ${userData.lastName}`,
              createdAt: serverTimestamp(),
            })
          }
        } else {
          const q = query(
            collection(db, 'subscribers'),
            where('email', '==', userData.email),
          )
          const snapshot = await getDocs(q)

          if (!snapshot.empty) {
            await deleteDoc(snapshot.docs[0].ref)
          }
        }
      }

      await updateDoc(userRef, {
        prefs,
        updatedAt: serverTimestamp(),
      })
      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
      return false
    }
  }

  // Get current user's data
  async function getCurrentUserData(): Promise<User | null> {
    const user = auth.currentUser
    if (!user?.uid) return null
    return getUser(user.uid)
  }

  // Update user profile
  async function updateUser(userData: Partial<User>): Promise<boolean> {
    if (!userData?.id) throw new Error('User ID is required')

    try {
      const userRef = doc(db, 'users', userData.id)
      await updateDoc(userRef, {
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone || null,
        updatedAt: serverTimestamp(),
      })

      // Log profile update activity
      await logActivity('PROFILE_UPDATED' as ActivityType, {
        updatedFields: {
          firstName: userData.firstName,
          lastName: userData.lastName,
          phone: userData.phone,
        },
      })

      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
      return false
    }
  }

  // Cleanup function
  function cleanup(): void {
    if (unsubscribe) {
      unsubscribe()
      unsubscribe = null
    }
  }

  return {
    users,
    sortedUsers,
    activeUsers,
    isLoading,
    error,
    success,
    subscribeToUsers,
    getUser,
    upsertUser,
    updateUserPreferences,
    getCurrentUserData,
    updateUser,
    cleanup,
  }
}
