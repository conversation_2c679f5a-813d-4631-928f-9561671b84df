import { Venue } from '@/models/Venue'
import { Timestamp } from 'firebase/firestore'
import type { PaymentSchema, EventNotes } from '@/models/Event'

export type EventStatus =
  | 'draft'
  | 'confirmed'
  | 'published'
  | 'cancelled'
  | 'postponed'
  | 'rescheduled'
  | 'save the date'

export type Act = {
  id: string
  name: string
  displayName?: string
  description?: string
  defaultGigDescription?: string
  website?: string
  logoUrls: {
    [key: string]: string
  }
  photoUrl?: string
  artistIds: string[]
  artists: {
    [key: string]: any
  }
  updatedAt: Timestamp
}

export type EventData = {
  id?: string
  title: string
  description?: string
  when: Date | Timestamp
  duration: number
  venue?: string
  venueDetails?: Venue | Record<string, any> | null
  acts?: string[]
  actDetails?: Act[] | Record<string, any>[]
  cta?: Record<string, any> | null
  notes?: EventNotes
  isPrivate?: boolean
  status?: EventStatus
}

export type UserPreferences = {
  isSubscribed: boolean
  viewAsGrid: boolean
}

export type RoleType = 'admin' | 'artist' | 'bandLeader'

export type RoleData = {
  actId?: string // ID of the act/band if role is 'bandLeader'
  artistId?: string // ID of the artist if role is 'artist'
}

export type UserRole = {
  [K in RoleType]?: RoleData
}

// Type for the User class constructor parameter
export type UserConstructorData = {
  id: string
  [key: string]: any // Allow any additional properties since we spread Firestore data
}

export type User = {
  id: string
  email?: string
  firstName?: string
  lastName?: string
  username?: string
  prefs?: UserPreferences
  tags?: string[]
  phone?: string
  birthday?: Timestamp | null
  address?: string
  isFrightened?: boolean
  roles?: UserRole[]
  photoURL?: string | null

  // Virtual properties from the User class
  fullName?: string
  displayName?: string
  roleNames?: string
}

// For form editing, we use a simpler interface
export type UserFormData = {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  roles: UserRole[]
}
